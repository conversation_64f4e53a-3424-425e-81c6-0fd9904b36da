import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    proxy: {
      // API路径代理
      '/api': {
        target: 'http://192.168.109.129:8080',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
      },
      // 认证相关API
      '/login': {
        target: 'http://192.168.109.129:8080',
        changeOrigin: true,
      },
      '/logout': {
        target: 'http://192.168.109.129:8080',
        changeOrigin: true,
      },
      '/verify': {
        target: 'http://192.168.109.129:8080',
        changeOrigin: true,
      },
      // 管理员API
      '/admin': {
        target: 'http://192.168.109.129:8080',
        changeOrigin: true,
      },
      // 系统设置API
      '/system-settings': {
        target: 'http://192.168.109.129:8080',
        changeOrigin: true,
      },
      // 注册相关API
      '/registration-status': {
        target: 'http://192.168.109.129:8080',
        changeOrigin: true,
      },
      '/registration-settings': {
        target: 'http://192.168.109.129:8080',
        changeOrigin: true,
      },
      '/login-info-settings': {
        target: 'http://192.168.109.129:8080',
        changeOrigin: true,
      },
    },
  },
})
