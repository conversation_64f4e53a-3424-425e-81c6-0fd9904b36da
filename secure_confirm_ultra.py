"""
自动确认发货模块 - 超级混淆版本
代码经过多层编码和混淆处理
"""
import base64 as LsWYPXmT
import zlib as oxWwRTDp
import types as AUdcGvRk
import binascii as qKAaznVW


class pDLWZWoi:
    
    def __init__(self):
        self.vrCYrtTq = "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"
    
    def gdVvQitT(self):
        step1_var = self.vrCYrtTq[::-1]
        step2_var = bytes.fromhex(step1_var)
        step3_var = LsWYPXmT.b64decode(step2_var)
        step4_var = oxWwRTDp.decompress(step3_var)
        step5_var = step4_var.decode('utf-8')

        return step5_var
    
    def CHsKCWCD(self):
        """创建模块"""
        decoded_code = self.gdVvQitT()
        module_obj = AUdcGvRk.ModuleType('secure_confirm')
        exec(decoded_code, module_obj.__dict__)
        return module_obj


OhPXQtOT = pDLWZWoi()

amBCLCwC = OhPXQtOT.CHsKCWCD()

SecureConfirm = amBCLCwC.SecureConfirm

# 清理所有变量
del OhPXQtOT
del amBCLCwC
del pDLWZWoi
